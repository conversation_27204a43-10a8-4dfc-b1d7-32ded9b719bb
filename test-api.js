// Test the Next.js API route
const http = require('http');

const testData = {
  group: 'group1',
  data: {
    a: "Buy Now",
    ptp: "2000", 
    psl: "1000",
    lot: "0.01",
    s: "XAUUSD",
    c: "SIG_TEST"
  }
};

const postData = JSON.stringify(testData);

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/webhook',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('Testing Next.js API route...');
console.log('Data:', testData);

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:', data);
  });
});

req.on('error', (err) => {
  console.log('Error:', err.message);
});

req.write(postData);
req.end();
