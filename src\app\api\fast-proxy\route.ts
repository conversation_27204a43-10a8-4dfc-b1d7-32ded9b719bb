import { NextRequest, NextResponse } from 'next/server';

const WEBHOOK_URL = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1;

export async function POST(request: NextRequest) {
  if (!WEBHOOK_URL) {
    return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
  }

  try {
    // Get JSON body directly
    const body = await request.json();
    
    // Fast forward - minimal headers, no logging
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // Return response directly
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch {
    return NextResponse.json({ error: 'Request failed' }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Fast JSON proxy - POST only',
    target: WEBHOOK_URL 
  });
}
