// Test ForexFactory with exact Postman headers
const https = require('https');

const testData = {
  default_view: "today",
  impacts: [3, 2],
  event_types: [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
  currencies: [9],
  begin_date: "2025-06-18",
  end_date: "2025-06-18"
};

function testWithPostmanHeaders() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'www.forexfactory.com',
      port: 443,
      path: '/calendar/apply-settings/1?navigation=0',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'User-Agent': 'PostmanRuntime/7.32.3',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('🔍 Testing ForexFactory with Postman headers...');
    console.log('POST data:', testData);
    console.log('Headers:', options.headers);

    const req = https.request(options, (res) => {
      console.log(`\n📊 Response Status: ${res.statusCode} ${res.statusMessage}`);
      console.log('📋 Response Headers:');
      Object.entries(res.headers).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`\n📏 Response length: ${responseData.length} characters`);
        console.log('🔍 First 500 characters:', responseData.substring(0, 500));
        
        try {
          const parsed = JSON.parse(responseData);
          console.log('\n✅ Valid JSON response!');
          console.log('📦 Response structure:', Object.keys(parsed));
          
          if (parsed.days && parsed.days[0] && parsed.days[0].events) {
            console.log(`\n📅 Found ${parsed.days[0].events.length} events for today`);
            console.log('\n🎯 Sample events:');
            parsed.days[0].events.slice(0, 5).forEach((event, i) => {
              console.log(`  ${i + 1}. ${event.time || 'No time'} - ${event.title || 'No title'} (${event.country || 'No country'}) [${event.impact || 'No impact'}]`);
            });
            
            console.log('\n🎉 SUCCESS! ForexFactory API is working!');
          } else {
            console.log('\n⚠️ No events found in response structure');
            console.log('Full response:', JSON.stringify(parsed, null, 2));
          }
          
          resolve({ status: res.statusCode, data: parsed });
        } catch (parseError) {
          console.log('\n❌ Response is not valid JSON');
          console.log('Parse error:', parseError.message);
          
          if (responseData.includes('<html')) {
            console.log('🌐 Response appears to be HTML (blocked or redirected)');
          } else if (responseData.includes('challenge')) {
            console.log('🛡️ Cloudflare challenge detected');
          }
          
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      console.log(`\n❌ Request Error: ${err.message}`);
      reject(err);
    });

    req.setTimeout(15000, () => {
      console.log('\n⏰ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function runTest() {
  try {
    await testWithPostmanHeaders();
    console.log('\n✅ Test completed!');
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  }
}

runTest();
