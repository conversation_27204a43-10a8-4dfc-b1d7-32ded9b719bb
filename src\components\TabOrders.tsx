// components/TabOrders.tsx
"use client";
import { useState, useEffect } from "react";
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";

// Using proxy API routes instead of direct webhook URLs

interface Position {
  ticket: number;
  symbol: string;
  type: "BUY" | "SELL";
  volume: number;
  entry: number;
  current: number;
  sl: number | null;
  tp: number | null;
  profit: number;
  comment: string;
}

interface PendingOrder {
  ticket: number;
  symbol: string;
  type: string;
  volume: number;
  entry: number;
  sl: number | null;
  tp: number | null;
  comment: string;
}

interface GroupData {
  [groupId: string]: {
    positions: Position[];
    pending: PendingOrder[];
    count: number;
  };
}

interface OrdersData {
  error: boolean;
  message?: string;
  data?: {
    positions: Position[];
    pending: PendingOrder[];
    sig_groups: GroupData;
    input_groups: GroupData;
    timestamp: number;
  };
}

export default function TabOrders({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object;
  sendWebhook: Function;
  loading: boolean;
}) {
  const [ordersData, setOrdersData] = useState<OrdersData | null>(null);
  const [activeSubTab, setActiveSubTab] = useState<"positions" | "pending" | "sig" | "input">("positions");
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Confirmation dialog state
  const { requireConfirmation } = useConfirmation();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    action: string;
    close_group: string;
    group_id: string;
  } | null>(null);

  // Auto-refresh every 5 seconds
  useEffect(() => {
    fetchOrdersData();
    const interval = setInterval(fetchOrdersData, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchOrdersData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const res = await fetch('/api/orders/data');
      // if (!response.ok) {
      //   throw new Error(`HTTP error! status: ${response.status}`);
      // }

      // Log response status for debugging
      // setLogs((prev) => [`📊 Proxy Response Status: ${res.status} ${res.statusText}`, ...prev]);

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status} ${res.statusText}`);
      }
      // const data: OrdersData = await response.json();
      const result = await res.json();
      if (result.error) {
        throw new Error(result.message || "Failed to fetch orders data");
      }
      const data: OrdersData = result.data

      setOrdersData(data);
      setLastUpdate(new Date());
    } catch (err) {
      console.error("Failed to fetch orders data:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch orders data");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseGroup = (closeGroup: "SIG" | "INPUT", groupId: string) => {
    const action = {
      action: "close_group",
      close_group: closeGroup,
      group_id: groupId,
    };

    if (requireConfirmation) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      executeCloseGroup(action);
    }
  };

  const executeCloseGroup = async (action: { action: string; close_group: string; group_id: string }) => {
    try {
      const response = await fetch('/api/orders/action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(action),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result.error) {
        throw new Error(result.message || "Failed to close group");
      }

      alert(`Successfully closed ${action.close_group} group: ${action.group_id}`);
      // Refresh data after successful action
      fetchOrdersData();
    } catch (err) {
      console.error("Failed to close group:", err);
      alert(`Failed to close group: ${err instanceof Error ? err.message : "Unknown error"}`);
    }
  };

  const handleConfirmSubmit = () => {
    if (pendingAction) {
      executeCloseGroup(pendingAction);
      setPendingAction(null);
    }
    setShowConfirmation(false);
  };

  const handleCancelSubmit = () => {
    setPendingAction(null);
    setShowConfirmation(false);
  };

  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toFixed(decimals);
  };

  const formatProfit = (profit: number) => {
    const color = profit >= 0 ? "text-green-400" : "text-red-400";
    const sign = profit >= 0 ? "+" : "";
    return <span className={color}>{sign}{formatNumber(profit)}</span>;
  };

  const renderPositionsTable = (positions: Position[]) => (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-white">
        <thead className="bg-gray-700">
          <tr>
            <th className="px-2 py-2 text-left">Comment</th>
            <th className="px-2 py-2 text-right">Profit</th>
            <th className="px-2 py-2 text-left">Ticket</th>
            <th className="px-2 py-2 text-left">Type</th>
            <th className="px-2 py-2 text-right">Volume</th>
            <th className="px-2 py-2 text-right">Entry</th>
            <th className="px-2 py-2 text-right">Current</th>
            <th className="px-2 py-2 text-right">SL</th>
            <th className="px-2 py-2 text-right">TP</th>
            <th className="px-2 py-2 text-left">Symbol</th>
          </tr>
        </thead>
        <tbody>
          {positions.map((pos) => (
            <tr key={pos.ticket} className="border-b border-gray-600 hover:bg-gray-700">
              <td className="px-2 py-2 text-xs">{pos.comment}</td>
              <td className="px-2 py-2 text-right">{formatProfit(pos.profit)}</td>
              <td className="px-2 py-2">{pos.ticket}</td>
              <td className="px-2 py-2">
                <span className={pos.type === "BUY" ? "text-green-400" : "text-red-400"}>
                  {pos.type}
                </span>
              </td>
              <td className="px-2 py-2 text-right">{formatNumber(pos.volume)}</td>
              <td className="px-2 py-2 text-right">{formatNumber(pos.entry, 5)}</td>
              <td className="px-2 py-2 text-right">{formatNumber(pos.current, 5)}</td>
              <td className="px-2 py-2 text-right">{pos.sl ? formatNumber(pos.sl, 5) : "-"}</td>
              <td className="px-2 py-2 text-right">{pos.tp ? formatNumber(pos.tp, 5) : "-"}</td>
              <td className="px-2 py-2">{pos.symbol}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const renderPendingTable = (pending: PendingOrder[]) => (
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-white">
        <thead className="bg-gray-700">
          <tr>
            <th className="px-2 py-2 text-left">Comment</th>
            <th className="px-2 py-2 text-left">Ticket</th>
            <th className="px-2 py-2 text-left">Type</th>
            <th className="px-2 py-2 text-right">Volume</th>
            <th className="px-2 py-2 text-right">Entry</th>
            <th className="px-2 py-2 text-right">SL</th>
            <th className="px-2 py-2 text-right">TP</th>
            <th className="px-2 py-2 text-left">Symbol</th>
          </tr>
        </thead>
        <tbody>
          {pending.map((order) => (
            <tr key={order.ticket} className="border-b border-gray-600 hover:bg-gray-700">
              <td className="px-2 py-2 text-xs">{order.comment}</td>
              <td className="px-2 py-2">{order.ticket}</td>
              <td className="px-2 py-2">
                <span className={order.type.includes("BUY") ? "text-green-400" : "text-red-400"}>
                  {order.type}
                </span>
              </td>
              <td className="px-2 py-2 text-right">{formatNumber(order.volume)}</td>
              <td className="px-2 py-2 text-right">{formatNumber(order.entry, 5)}</td>
              <td className="px-2 py-2 text-right">{order.sl ? formatNumber(order.sl, 5) : "-"}</td>
              <td className="px-2 py-2 text-right">{order.tp ? formatNumber(order.tp, 5) : "-"}</td>
              <td className="px-2 py-2">{order.symbol}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-white">Orders & Positions</h2>
          {lastUpdate && (
            <p className="text-sm text-gray-400">
              Last updated: {lastUpdate.toLocaleTimeString()}
              {isLoading && <span className="ml-2 text-yellow-400">Refreshing...</span>}
            </p>
          )}
        </div>
        <button
          onClick={fetchOrdersData}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? "Refreshing..." : "Refresh Now"}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-900 border border-red-600 text-red-200 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Sub-tabs */}
      <div className="flex space-x-2 border-b border-gray-600">
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeSubTab === "positions" ? "border-b-2 border-blue-400 text-blue-400" : "text-gray-400"
          }`}
          onClick={() => setActiveSubTab("positions")}
        >
          Positions ({ordersData?.data?.positions.length || 0})
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeSubTab === "pending" ? "border-b-2 border-blue-400 text-blue-400" : "text-gray-400"
          }`}
          onClick={() => setActiveSubTab("pending")}
        >
          Pending ({ordersData?.data?.pending.length || 0})
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeSubTab === "sig" ? "border-b-2 border-blue-400 text-blue-400" : "text-gray-400"
          }`}
          onClick={() => setActiveSubTab("sig")}
        >
          SIG Groups ({Object.keys(ordersData?.data?.sig_groups || {}).length})
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium ${
            activeSubTab === "input" ? "border-b-2 border-blue-400 text-blue-400" : "text-gray-400"
          }`}
          onClick={() => setActiveSubTab("input")}
        >
          INPUT Groups ({Object.keys(ordersData?.data?.input_groups || {}).length})
        </button>
      </div>

      {/* Content */}
      <div className="bg-gray-800 rounded-lg p-4">
        {!ordersData || !ordersData.data ? (
          <div className="text-center text-gray-400 py-8">
            {isLoading ? "Loading orders data..." : "No data available"}
          </div>
        ) : (
          <>
            {activeSubTab === "positions" && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Open Positions</h3>
                {ordersData.data.positions.length === 0 ? (
                  <p className="text-gray-400">No open positions</p>
                ) : (
                  renderPositionsTable(ordersData.data.positions)
                )}
              </div>
            )}

            {activeSubTab === "pending" && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Pending Orders</h3>
                {ordersData.data.pending.length === 0 ? (
                  <p className="text-gray-400">No pending orders</p>
                ) : (
                  renderPendingTable(ordersData.data.pending)
                )}
              </div>
            )}

            {activeSubTab === "sig" && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">SIG Groups</h3>
                {Object.keys(ordersData.data.sig_groups).length === 0 ? (
                  <p className="text-gray-400">No SIG groups found</p>
                ) : (
                  <div className="space-y-4">
                    {Object.entries(ordersData.data.sig_groups).map(([groupId, group]) => (
                      <div key={groupId} className="border border-gray-600 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-3">
                          <div>
                            <h4 className="text-lg font-medium text-white">SIG Group: {groupId}</h4>
                            <p className="text-sm text-gray-400">
                              {group.positions.length} positions, {group.pending.length} pending orders
                            </p>
                          </div>
                          <button
                            onClick={() => handleCloseGroup("SIG", groupId)}
                            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                          >
                            Close Group
                          </button>
                        </div>
                        
                        {group.positions.length > 0 && (
                          <div className="mb-4">
                            <h5 className="text-md font-medium text-white mb-2">Positions:</h5>
                            {renderPositionsTable(group.positions)}
                          </div>
                        )}
                        
                        {group.pending.length > 0 && (
                          <div>
                            <h5 className="text-md font-medium text-white mb-2">Pending Orders:</h5>
                            {renderPendingTable(group.pending)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeSubTab === "input" && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">INPUT Groups</h3>
                {Object.keys(ordersData.data.input_groups).length === 0 ? (
                  <p className="text-gray-400">No INPUT groups found</p>
                ) : (
                  <div className="space-y-4">
                    {Object.entries(ordersData.data.input_groups).map(([groupId, group]) => (
                      <div key={groupId} className="border border-gray-600 rounded-lg p-4">
                        <div className="flex justify-between items-center mb-3">
                          <div>
                            <h4 className="text-lg font-medium text-white">INPUT Group: {groupId}</h4>
                            <p className="text-sm text-gray-400">
                              {group.positions.length} positions, {group.pending.length} pending orders
                            </p>
                          </div>
                          <button
                            onClick={() => handleCloseGroup("INPUT", groupId)}
                            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                          >
                            Close Group
                          </button>
                        </div>
                        
                        {group.positions.length > 0 && (
                          <div className="mb-4">
                            <h5 className="text-md font-medium text-white mb-2">Positions:</h5>
                            {renderPositionsTable(group.positions)}
                          </div>
                        )}
                        
                        {group.pending.length > 0 && (
                          <div>
                            <h5 className="text-md font-medium text-white mb-2">Pending Orders:</h5>
                            {renderPendingTable(group.pending)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {/* Confirmation Dialog */}
      {showConfirmation && pendingAction && (
        <ConfirmationDialog
          isOpen={showConfirmation}
          title="Confirm Close Group"
          message={`Are you sure you want to close all positions and pending orders for ${pendingAction.close_group} group "${pendingAction.group_id}"?`}
          onConfirm={handleConfirmSubmit}
          onCancel={handleCancelSubmit}
        />
      )}
    </div>
  );
}
