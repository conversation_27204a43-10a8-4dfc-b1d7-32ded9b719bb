// components/TabCalendar.tsx
"use client";
import { useState } from "react";
import Select from "react-select";
import { SYMBOL_OPTIONS, SYMBOL_COUNTRY_MAP } from "@/config/global";

// Generate iframe URL based on selected symbol
const generateCalendarUrl = (symbol: string): string => {
  const countries = SYMBOL_COUNTRY_MAP[symbol] || "5"; // Default to US
  const baseUrl = "https://sslecal2.investing.com";
  const params = new URLSearchParams({
    ecoDayBackground: "transparent", // Make background transparent
    defaultFont: "#333333",
    columns: "exc_flags,exc_currency,exc_importance,exc_actual,exc_forecast,exc_previous",
    category: "_employment,_economicActivity,_inflation,_credit,_centralBanks,_confidenceIndex,_balance,_Bonds",
    importance: "2,3",
    features: "datepicker,timezone,timeselector,filters",
    countries: countries,
    calType: "day",
    timeZone: "27",
    lang: "1"
  });

  return `${baseUrl}?${params.toString()}`;
};



export default function TabCalendar({
  customStyle,
  sendWebhook,
  loading: _loading,
}: {
  customStyle: Object,
  sendWebhook: Function;
  loading: boolean;
}) {
  const [data, setData] = useState({
    s: "XAUUSD",
  });

  const [iframeKey, setIframeKey] = useState(0); // For forcing iframe refresh

  const refreshCalendar = () => {
    setIframeKey(prev => prev + 1); // Force iframe refresh
  };

  return (
    <div className="grid grid-cols-3 gap-4">


      {/* Economic Calendar Iframe */}
      <div className="rounded-lg col-span-2">
        <h3 className="text-lg font-semibold text-white mb-4">
          📅 Economic Calendar - {data.s} Related Events
          <span className="text-sm font-normal text-green-400 ml-2">(Live Widget)</span>
        </h3>

        <div className="relative w-full" style={{ height: '600px',  width: '100%' ,  minWidth: '600px'  }}>
          <iframe
            key={iframeKey}
            src={generateCalendarUrl(data.s)}
            width="100%"
            height="100%"
            className="rounded-lg border-0 bg-transparent"
            title={`Economic Calendar for ${data.s}`}
            style={{
              border: 'none',
              margin: 0,
              backgroundColor: 'transparent',
              background: 'transparent'
            }}
          />
        </div>

        <div className="mt-2 text-xs text-gray-400 text-center">
          Economic calendar data provided by{' '}
          <a
            href="https://www.investing.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-400 hover:text-blue-300"
          >
            Investing.com
          </a>
        </div>
        
      </div>

      {/* <div className="space-y-6"> */}
      {/* Symbol Selection */}
      {/* <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center"> */}
      <div className="">
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200 mb-2">Trading Symbol</label>
          <Select
            className="text-black"
            value={SYMBOL_OPTIONS.find((opt) => opt.value === data.s)}
            onChange={(e) => setData({ ...data, s: e?.value || "" })}
            options={SYMBOL_OPTIONS}
            isSearchable
            styles={customStyle}
          />
        </div>

        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200 mb-2">Calendar Countries</label>
          <div className="text-sm text-gray-300 bg-gray-800 p-3 rounded-md flex justify-between items-center">
            <span>Countries: {SYMBOL_COUNTRY_MAP[data.s] || "5 (US)"}</span>
            <button
              onClick={refreshCalendar}
              className="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
              title="Refresh calendar"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>


    </div>
  );
}
