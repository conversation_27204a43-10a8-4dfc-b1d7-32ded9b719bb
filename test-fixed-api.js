// Test the fixed API with correct parameters
const http = require('http');

const testCases = [
  {
    name: "Group 1 - Corrected format",
    data: {
      group: 'group1',
      data: {
        a: "Buy Now",
        s: "XAUUSD", 
        p: "1950.00"
      }
    }
  },
  {
    name: "Group 2 - Full format",
    data: {
      group: 'group2',
      data: {
        rows: [{ tp: "2000", lot: "0.01" }],
        a: "Buy Limit",
        p: "1950.00",
        sl: "1900",
        s: "XAUUSD",
        c: "SIG_TEST"
      }
    }
  }
];

function testAPI(testCase) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testCase.data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/webhook',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n${'='.repeat(60)}`);
    console.log(`Testing: ${testCase.name}`);
    console.log(`Data: ${postData}`);
    console.log(`${'='.repeat(60)}`);

    const req = http.request(options, (res) => {
      console.log(`API Status: ${res.statusCode} ${res.statusMessage}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`API Response: ${responseData}`);
        
        try {
          const parsed = JSON.parse(responseData);
          if (parsed.success) {
            console.log('✅ API SUCCESS!');
            if (parsed.data && parsed.data.error === false) {
              console.log('🎉 WEBHOOK SUCCESS!');
            }
          } else {
            console.log('❌ API reported failure');
          }
        } catch (e) {
          console.log('⚠️ Non-JSON response');
        }
        
        resolve({ status: res.statusCode, data: responseData });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request Error: ${err.message}`);
      console.log('💡 Make sure your Next.js server is running on port 3000');
      reject(err);
    });

    req.setTimeout(10000);
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🔍 Testing fixed API with correct parameters...\n');
  
  for (const testCase of testCases) {
    try {
      await testAPI(testCase);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
    } catch (error) {
      console.log(`Failed: ${error.message}`);
    }
  }
  
  console.log('\n✅ All tests completed!');
}

runTests();
