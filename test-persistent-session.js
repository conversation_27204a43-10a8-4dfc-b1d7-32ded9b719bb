// Test the persistent session API
const http = require('http');

async function testSessionStatus() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/forexfactory',
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    };

    console.log('🔍 Checking session status...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('📊 Session Status:', result);
          resolve(result);
        } catch (error) {
          console.log('❌ Failed to parse session status:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (err) => {
      console.log('❌ Session status request failed:', err.message);
      reject(err);
    });

    req.setTimeout(5000);
    req.end();
  });
}

async function testForexFactoryAPI() {
  return new Promise((resolve, reject) => {
    const testData = {
      default_view: "today",
      impacts: [3, 2],
      event_types: [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
      currencies: [9],
      begin_date: "2025-06-18",
      end_date: "2025-06-18"
    };

    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/forexfactory',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log('\n🚀 Testing ForexFactory API with persistent session...');
    console.log('📤 POST data:', testData);

    const req = http.request(options, (res) => {
      console.log(`📊 API Status: ${res.statusCode} ${res.statusMessage}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`📏 Response length: ${responseData.length} characters`);
        
        try {
          const parsed = JSON.parse(responseData);
          console.log('📦 Response structure:', Object.keys(parsed));
          
          if (parsed.success && parsed.data?.days?.[0]?.events) {
            console.log(`\n✅ SUCCESS! Found ${parsed.data.days[0].events.length} events`);
            console.log('\n🎯 Sample events:');
            parsed.data.days[0].events.slice(0, 3).forEach((event, i) => {
              console.log(`  ${i + 1}. ${event.time || 'No time'} - ${event.title || 'No title'} (${event.country || 'No country'}) [${event.impact || 'No impact'}]`);
            });
          } else if (parsed.error) {
            console.log(`\n❌ API Error: ${parsed.message}`);
            if (parsed.shouldRetry) {
              console.log('🔄 API suggests retry (session may be establishing)');
            }
          } else {
            console.log('\n⚠️ Unexpected response format');
            console.log('Full response:', JSON.stringify(parsed, null, 2));
          }
          
          resolve(parsed);
        } catch (parseError) {
          console.log('\n❌ Response is not valid JSON');
          console.log('Parse error:', parseError.message);
          console.log('Raw response:', responseData.substring(0, 500));
          resolve({ error: true, message: 'Invalid JSON response' });
        }
      });
    });

    req.on('error', (err) => {
      console.log(`\n❌ Request Error: ${err.message}`);
      console.log('💡 Make sure your Next.js server is running on port 3000');
      reject(err);
    });

    req.setTimeout(30000); // 30 second timeout for session establishment
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  try {
    console.log('🔍 Testing ForexFactory Persistent Session API...\n');
    
    // Test 1: Check session status
    await testSessionStatus();
    
    // Test 2: Try ForexFactory API
    const result = await testForexFactoryAPI();
    
    // Test 3: Check session status again
    console.log('\n🔄 Checking session status after API call...');
    await testSessionStatus();
    
    console.log('\n✅ All tests completed!');
    
    if (result.success) {
      console.log('\n🎉 ForexFactory API is working with persistent session!');
    } else {
      console.log('\n⚠️ ForexFactory API failed, but session management is working');
    }
    
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  }
}

runTests();
