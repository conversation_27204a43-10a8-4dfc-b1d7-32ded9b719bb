// Test ForexFactory with proper session handling
const https = require('https');

// Step 1: Visit the calendar page to get cookies
function getCalendarPage() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'www.forexfactory.com',
      port: 443,
      path: '/calendar',
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      }
    };

    console.log('Step 1: Getting calendar page to establish session...');

    const req = https.request(options, (res) => {
      console.log(`Calendar page status: ${res.statusCode}`);
      
      // Extract cookies
      const cookies = res.headers['set-cookie'] || [];
      console.log('Received cookies:', cookies.length);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({ cookies, html: data });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000);
    req.end();
  });
}

// Step 2: Make API call with cookies
function makeApiCall(cookies) {
  return new Promise((resolve, reject) => {
    const testData = {
      default_view: "today",
      impacts: [3, 2],
      event_types: [1, 2, 3, 4, 5, 7, 8, 9, 10, 11],
      currencies: [9],
      begin_date: "2025-06-18",
      end_date: "2025-06-18"
    };

    const postData = JSON.stringify(testData);
    
    // Format cookies for request
    const cookieHeader = cookies.map(cookie => cookie.split(';')[0]).join('; ');
    
    const options = {
      hostname: 'www.forexfactory.com',
      port: 443,
      path: '/calendar/apply-settings/1?navigation=0',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.forexfactory.com/calendar',
        'Origin': 'https://www.forexfactory.com',
        'Connection': 'keep-alive',
        'Cookie': cookieHeader,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      }
    };

    console.log('\nStep 2: Making API call with session cookies...');
    console.log('Cookie header:', cookieHeader.substring(0, 100) + '...');
    console.log('POST data:', testData);

    const req = https.request(options, (res) => {
      console.log(`API Status: ${res.statusCode} ${res.statusMessage}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`Response length: ${responseData.length} characters`);
        console.log('First 500 characters:', responseData.substring(0, 500));
        
        try {
          const parsed = JSON.parse(responseData);
          console.log('✅ Valid JSON response!');
          console.log('Response structure:', Object.keys(parsed));
          
          if (parsed.days && parsed.days[0] && parsed.days[0].events) {
            console.log(`📅 Found ${parsed.days[0].events.length} events for today`);
            parsed.days[0].events.slice(0, 3).forEach((event, i) => {
              console.log(`Event ${i + 1}:`, {
                title: event.title,
                country: event.country,
                time: event.time,
                impact: event.impact,
                currency: event.currency
              });
            });
          }
          
          resolve({ status: res.statusCode, data: parsed });
        } catch (parseError) {
          console.log('❌ Response is not valid JSON');
          console.log('Parse error:', parseError.message);
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(15000);
    req.write(postData);
    req.end();
  });
}

async function runTest() {
  try {
    console.log('🔍 Testing ForexFactory with proper session handling...\n');
    
    // Step 1: Get session cookies
    const { cookies } = await getCalendarPage();
    
    if (cookies.length === 0) {
      console.log('⚠️ No cookies received from calendar page');
      return;
    }
    
    // Wait a moment to simulate human behavior
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Step 2: Make API call with cookies
    await makeApiCall(cookies);
    
    console.log('\n✅ Test completed!');
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  }
}

runTest();
