// Debug webhook parameters
const http = require('http');

const WEBHOOK_GROUP_1 = 'http://nas-pm.suksapan.or.th:2249/webhook_instant';

// Test different parameter combinations for Group 1
const testCases = [
  {
    name: "Original Group 1 format",
    data: {
      a: "Buy Now",
      ptp: "2000",
      psl: "1000", 
      lot: "0.01",
      s: "XAUUSD",
      c: "SIG_TEST"
    }
  },
  {
    name: "With price parameter",
    data: {
      a: "Buy Now",
      p: "1950.00",
      ptp: "2000",
      psl: "1000",
      lot: "0.01", 
      s: "XAUUSD",
      c: "SIG_TEST"
    }
  },
  {
    name: "Minimal required fields",
    data: {
      a: "Buy Now",
      s: "XAUUSD",
      p: "1950.00"
    }
  },
  {
    name: "Alternative field names",
    data: {
      action: "Buy Now",
      symbol: "XAUUSD", 
      price: "1950.00",
      tp: "2000",
      sl: "1000",
      lot: "0.01"
    }
  }
];

function testWebhook(url, testCase) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testCase.data);
    
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n${'='.repeat(60)}`);
    console.log(`Testing: ${testCase.name}`);
    console.log(`Data: ${postData}`);
    console.log(`${'='.repeat(60)}`);

    const req = http.request(options, (res) => {
      console.log(`Status: ${res.statusCode} ${res.statusMessage}`);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`Response: ${responseData}`);
        
        if (responseData.includes('Success') || responseData.includes('"error":false')) {
          console.log('✅ SUCCESS!');
        } else if (responseData.includes('Missing')) {
          console.log('❌ Missing required fields');
        } else if (responseData.includes('Internal Server Error')) {
          console.log('💥 Server error');
        } else {
          console.log('⚠️ Unknown response');
        }
        
        resolve({ status: res.statusCode, data: responseData });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request Error: ${err.message}`);
      reject(err);
    });

    req.setTimeout(10000);
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🔍 Debugging webhook parameters...\n');
  
  for (const testCase of testCases) {
    try {
      await testWebhook(WEBHOOK_GROUP_1, testCase);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
    } catch (error) {
      console.log(`Failed: ${error.message}`);
    }
  }
  
  console.log('\n✅ All tests completed!');
}

runTests();
