// Test ForexFactory API directly
const http = require('http');

const testData = {
  default_view: "today",
  impacts: [3, 2], // High and Medium impact events
  event_types: [1, 2, 3, 4, 5, 7, 8, 9, 10, 11], // All event types
  currencies: [9], // USD currency ID
  begin_date: "2025-06-18",
  end_date: "2025-06-18"
};

function testForexFactory() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'www.forexfactory.com',
      port: 443,
      path: '/calendar/apply-settings/1?navigation=0',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.forexfactory.com/calendar',
        'Origin': 'https://www.forexfactory.com',
        'Connection': 'keep-alive',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      }
    };

    console.log('Testing ForexFactory API...');
    console.log('POST data:', testData);
    console.log('URL: https://www.forexfactory.com/calendar/apply-settings/1?navigation=0');

    const https = require('https');
    const req = https.request(options, (res) => {
      console.log(`Status: ${res.statusCode} ${res.statusMessage}`);
      console.log('Headers:', res.headers);
      
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        console.log(`Response length: ${responseData.length} characters`);
        console.log('First 500 characters:', responseData.substring(0, 500));
        
        try {
          const parsed = JSON.parse(responseData);
          console.log('✅ Valid JSON response');
          console.log('Response structure:', Object.keys(parsed));
          
          if (parsed.days && parsed.days[0] && parsed.days[0].events) {
            console.log(`📅 Found ${parsed.days[0].events.length} events for today`);
            parsed.days[0].events.slice(0, 3).forEach((event, i) => {
              console.log(`Event ${i + 1}:`, {
                title: event.title,
                country: event.country,
                time: event.time,
                impact: event.impact,
                currency: event.currency
              });
            });
          } else {
            console.log('⚠️ No events found in response structure');
          }
          
          resolve({ status: res.statusCode, data: parsed });
        } catch (parseError) {
          console.log('❌ Response is not valid JSON');
          console.log('Parse error:', parseError.message);
          
          if (responseData.includes('<html')) {
            console.log('🌐 Response appears to be HTML (possibly blocked or redirected)');
          }
          
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request Error: ${err.message}`);
      reject(err);
    });

    req.setTimeout(15000);
    req.write(postData);
    req.end();
  });
}

async function runTest() {
  try {
    await testForexFactory();
    console.log('\n✅ Test completed!');
  } catch (error) {
    console.log(`\n❌ Test failed: ${error.message}`);
  }
}

runTest();
