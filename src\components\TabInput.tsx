// components/TabInput.tsx
"use client";
import { useState, useEffect } from "react";
import Select from "react-select";
import { SYMBOL_OPTIONS, LOT_DISTRIBUTION_CONFIG, mapActionToValid } from "@/config/global";
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";
import { getOrGenerateSignalId, extractSignalId } from "@/utils/signalId";

const WEBHOOK_GROUP_2 = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2;

type Row = { tp: number; lot: number };

export default function TabInput({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object,
  sendWebhook: Function;
  loading: boolean;
}) {
  const [rows, setRows] = useState<Row[]>([{ tp: 0.0, lot: 0.01 }]);
  const [text, setText] = useState("");
  const [priceChoices, setPriceChoices] = useState<string[]>([]);
  const [maxLotPerMessage, setMaxLotPerMessage] = useState(1.0);
  const [lotConfig, setLotConfig] = useState(LOT_DISTRIBUTION_CONFIG);
  const [data, setData] = useState({
    a: "Buy Limit",
    p: 0.0,
    sl: 0.0,
    s: "XAUUSD",
    c: "SIG_INPUT",
    id: "",
  });

  // Get confirmation setting from context
  const { requireConfirmation } = useConfirmation();

  // Confirmation dialog state
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Load lot configuration and generate initial Signal ID on component mount
  useEffect(() => {
    loadLotConfig();
    // Generate initial Signal ID if not set
    if (!data.id) {
      setData(prev => ({ ...prev, id: getOrGenerateSignalId("") }));
    }
  }, []);

  const loadLotConfig = async () => {
    // Use the global config directly - no need to fetch from JSON file
    setLotConfig(LOT_DISTRIBUTION_CONFIG);
    setMaxLotPerMessage(LOT_DISTRIBUTION_CONFIG.maxLotPerMessage);
  };

  const handleChange = (index: number, field: keyof Row, value: number) => {
    const updated = [...rows];
    // Keep as number but round to 2 decimal places
    updated[index][field] = value;
    setRows(updated);
  };

  const addRow = () => {
    if (rows.length < 10) {
      setRows([...rows, { tp: 0.0, lot: 0.01 }]);
    }
  };

  const removeRow = (index: number) => {
    const updated = rows.filter((_, i) => i !== index);
    setRows(updated);
  };

  const distributeLotsAutomatically = () => {
    const tpCount = rows.length;
    const distributionKey = tpCount as keyof typeof lotConfig.lotDistribution;
    const distribution = lotConfig.lotDistribution[distributionKey] || [100];

    const updatedRows = rows.map((row, index) => {
      const percentage = distribution[index] || 1; // Fallback to 1% if index exceeds distribution array
      let calculatedLot = (maxLotPerMessage * percentage) / 100;

      // Ensure minimum lot size
      const minLot = (lotConfig as any).minLotSize || 0.01;
      if (calculatedLot < minLot) {
        calculatedLot = minLot;
      }

      return {
        ...row,
        lot: parseFloat(calculatedLot.toFixed(2))
      };
    });

    setRows(updatedRows);
  };
 
  const handleSubmitClick = () => {
    if (requireConfirmation) {
      setShowConfirmation(true);
    } else {
      // Send immediately without confirmation
      handleDirectSubmit();
    }
  };

  const handleDirectSubmit = () => {
    // const webhookData = formatDataForWebhook();
    sendWebhook("g_input", WEBHOOK_GROUP_2, {rows, ...data});
  };

  const handleConfirmSubmit = () => {
    handleDirectSubmit();
    setShowConfirmation(false);
  };

  const handleCancelSubmit = () => {
    setShowConfirmation(false);
  };

  const parseLongText = (textarea: string) => {
    // setText(textarea);
    const lines = textarea.split("\n").map((line) => line.trim());
    const newRows: Row[] = [];
    let newPriceChoices: string[] = [];

    // Extract or generate Signal ID
    const signalId = getOrGenerateSignalId(textarea);
    setData((prev) => ({ ...prev, id: signalId }));

    lines.forEach((line) => {
      if (line.toLowerCase().startsWith("tp")) {
        const match = line.match(/TP\d\s*:\s*(\d+(\.\d+)?)/i);
        if (match) {
          newRows.push({ tp: parseFloat(match[1]), lot: 0.01 });
        }
      }
      const slMatch = line.match(/SL\s*:\s*(\d+(\.\d+)?)/i);
      if (slMatch) {
        setData((prev) => ({ ...prev, sl: parseFloat(slMatch[1]) }));
      }

      const symbolMatch = line.match(/Symbol\s*:\s*(\w+)/i);
      if (symbolMatch) {
        setData((prev) => ({ ...prev, s: symbolMatch[1].toUpperCase() }));
      }

      const signalMatch = line.match(/Signal\s*:\s*(.+)/i);
      if (signalMatch) {
        const rawAction = signalMatch[1].trim();
        const mappedAction = mapActionToValid(rawAction);
        setData((prev) => ({ ...prev, a: mappedAction }));
      }

      const priceMatch = line.match(/Price\s*:\s*(\d+(\.\d+)?)\s*[-–]\s*(\d+(\.\d+)?)/i);
      if (priceMatch) {
        const start = parseFloat(priceMatch[1]);
        const end = parseFloat(priceMatch[3]);
        const mid = ((start + end) / 2).toFixed(2);
        newPriceChoices = [start.toFixed(2), mid, end.toFixed(2)];
        setPriceChoices(newPriceChoices);
        setData((prev) => ({ ...prev, p: parseFloat(mid) }));
      }

      const commentMatch = line.match(/C\.(\w+)/i);
      if (commentMatch) {
        setData((prev) => ({ ...prev, c: `SIG_C_${commentMatch[1]}_${signalId}` }));
        // setData((prev) => ({ ...prev, c: `SIG_C_${commentMatch[1]}_${signalId}` }));
      }
    });

    // Update comment to include Signal ID if not already set by C. pattern
    if (!textarea.match(/C\.\w+/i)) {
      setData((prev) => ({ ...prev, c: `INPUT_MANUAL_${signalId}` }));
    }

    if (newRows.length > 0) {
      const limitedRows = newRows.slice(0, 10);
      setRows(limitedRows);

      // Auto-apply lot distribution after parsing
      // setTimeout(() => {
      //   distributeLotsAutomatically();
      // }, 100);
    }
  };

  const copyToClipboard = async () => {
    try {
      if (!navigator.clipboard || !navigator.clipboard.writeText) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert("Text copied to clipboard!");
        return;
      }

      await navigator.clipboard.writeText(text);
      alert("Text copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy text: ", err);
      alert("Failed to copy text to clipboard. Please select and copy the text manually.");
    }
  };

  const pasteFromClipboard = async () => {
    try {
      // Check if clipboard API is available
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        alert("Clipboard API not supported. Please paste manually using Ctrl+V in the textarea.");
        return;
      }

      // Check permissions
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });
      if (permission.state === 'denied') {
        alert("Clipboard access denied. Please paste manually using Ctrl+V in the textarea.");
        return;
      }

      const clipboardText = await navigator.clipboard.readText();
      if (clipboardText) {
        setText(clipboardText);
        parseLongText(clipboardText);
        // alert("Text pasted and parsed successfully!");
      } else {
        alert("No text found in clipboard");
      }
    } catch (err) {
      console.error("Failed to paste text: ", err);
      // Provide user-friendly error message with alternative
      alert("Failed to paste from clipboard. This might be due to browser security restrictions. Please paste manually using Ctrl+V in the textarea above.");
    }
  };
 
  const priceOptions = priceChoices.map((p) => ({ label: p, value: p }));

  return (
    <div className="space-y-4">
      {/* Long Text */}
      <div>
        <label className="block text-sm font-medium text-gray-200">
          Long Text <span className="text-gray-400 text-xs">(Ctrl+V)</span>
        </label>
        <textarea
          value={text}
          onChange={(e) => {setText(e.target.value);parseLongText(e.target.value);}}
          // onChange={(e) => setText(e.target.value)}
          rows={6}
          className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
        />
        <div className="mt-2 flex gap-2">
          {/* <button
            onClick={() => parseLongText(text)}
            className="px-4 py-1 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            📥 Extract
          </button> */}
          <button
            onClick={copyToClipboard}
            className="px-4 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            📋 Copy
          </button>
          <button
            onClick={pasteFromClipboard}
            className="px-4 py-1 bg-green-600 text-white rounded hover:bg-green-700"
          >
            📄 Paste
          </button>
        </div>
      </div>

      {/* Dynamic Rows A / B */}
      {rows.map((row, index) => (
        <div
          key={index}
          className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center"
        >
          <div className="w-full">
            <label className="block text-sm font-medium text-gray-200">
              TP{index + 1}
            </label>
            <input
              type="text"
              placeholder={`TP${index + 1}`}
              className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
              value={row.tp.toFixed(2)}
              onChange={(e) => handleChange(index, "tp", parseFloat(Number(e.target.value).toFixed(2)))}
            />
          </div>
          <div className="w-full">
            <label className="block text-sm font-medium text-gray-200">
              Lot
            </label>
            <div className="flex gap-2 items-center">
              <input
                type="text"
                className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
                value={row.lot}
                onChange={(e) => handleChange(index, "lot", parseFloat(e.target.value))}
              />
              {rows.length > 1 && (
                <button
                  onClick={() => removeRow(index)}
                  className="text-red-400 hover:text-red-600 text-xl"
                >
                  ✖
                </button>
              )}
            </div>
          </div>
        </div>
      ))}

      {/* Add Row Button */}
      {/* <div> */}
        
      <div className="grid grid-cols-3 ">
        
        <div className="">
          <button
            onClick={addRow}
            disabled={rows.length >= 10}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 disabled:opacity-50"
          >
            ➕ TP
          </button>
        </div>
        <div className="col-span-2 text-right">
          {/* <span className="ml-4">Max Lot</span> */}
          <button
            onClick={distributeLotsAutomatically}
            className="py-2 px-4 ml-4 bg-sky-600 text-white font-semibold rounded-md hover:bg-blue-700"
            title="Automatically distribute lots based on TP count"
          >
             Distribute Lots ⋙ {/*⋘ ⋙*/}
          </button>
          <input
            type="number"
            step="0.01"
            min="0.01"
            value={maxLotPerMessage}
            onChange={(e) => setMaxLotPerMessage(parseFloat(e.target.value) || 0.01)}
            className="w-1/6 ml-2 mt-1 text-center rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            placeholder="1.00"
          /> 
        </div>
      </div>
      {/* Distribution Logic (Same as Discord Bot):
        TP Count	Distribution
        1 TP	100%
        2 TPs	70%, 30%
        3 TPs	70%, 20%, 10%
        4 TPs	65%, 20%, 10%, 5%
        5 TPs	50%, 25%, 15%, 9%, 1% 
      */}

      {/* Smart Lot Distribution */}
      <div className="flex flex-col sm:flex-row grid grid-cols-1 lg:grid-cols-1 gap-4 text-right mb-4">
        
          <div className="mb-2 text-xs text-gray-400">
            {rows.length} TP{rows.length !== 1 ? 's' : ''}: {
              (() => {
                const distributionKey = rows.length as keyof typeof lotConfig.lotDistribution;
                const distribution = lotConfig.lotDistribution[distributionKey] || [100];
                return distribution.slice(0, rows.length).join('%, ') + '%';
              })()
            }
          </div> 
      </div>

      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Symbol */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Symbol</label>
          <Select
            className="text-black"
            value={SYMBOL_OPTIONS.find((opt) => opt.value === data.s)}
            onChange={(e) => setData({ ...data, s: e?.value || "" })}
            options={SYMBOL_OPTIONS}
            isSearchable
            styles={customStyle}
          />
        </div>

        {/* Signal Type */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">
            Action
          </label>
          <select
            value={data.a}
            onChange={(e) => setData({ ...data, a: e.target.value })}
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
          >
            <option value="Sell Limit">Sell Limit</option>
            <option value="Buy Limit">Buy Limit</option>
          </select>
        </div>
      </div>
      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Price Select */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Entry Price</label>
          <Select
            options={priceOptions}
            value={
              priceOptions.find((opt) => parseFloat(opt.value) === data.p) || {
                label: data.p.toFixed(2),
                value: data.p.toFixed(2),
              }
            }
            onChange={(selected) =>
              setData((prev) => ({
                ...prev,
                p: parseFloat(selected?.value || "0"),
              }))
            }
            onInputChange={(inputValue) => {
              const floatVal = parseFloat(inputValue);
              if (!isNaN(floatVal)) {
                setData((prev) => ({
                  ...prev,
                  p: parseFloat(floatVal.toFixed(2)),
                }));
              }
            }}
            isClearable
            isSearchable
            placeholder="เลือกราคาหรือพิมพ์เอง"
            className="text-black"
            styles={customStyle}
          />
        </div>

        {/* SL */}
        {/* <div className="w-full sm:w-1/2"> */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">SL</label>
          <input
            type="text"
            className="w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            value={data.sl.toFixed(2)}
            onChange={(e) => setData({ ...data, sl: parseFloat(Number(e.target.value).toFixed(2)) })}
          />
        </div>
      </div>

      {/* Signal ID and Comment in one row */}
      <div className="flex flex-col sm:flex-row grid grid-cols-1 lg:grid-cols-2 gap-4 items-center">
        {/* Signal ID */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Signal ID</label>
          <div className="flex gap-2">
            <input
              type="text"
              className="mt-1 flex-1 rounded-md bg-gray-800 text-white border border-gray-600 p-2"
              value={data.id}
              onChange={(e) => setData({ ...data, id: e.target.value })}
              placeholder="Auto-generated or extracted from text"
            />
            <button
              type="button"
              onClick={() => setData(prev => ({ ...prev, id: getOrGenerateSignalId("") }))}
              className="mt-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              title="Generate new Signal ID"
            >
              🔄
            </button>
          </div>
        </div>

        {/* Comment */}
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-200">Comment</label>
          <input
            type="text"
            className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            value={data.c}
            onChange={(e) => setData({ ...data, c: e.target.value })}
          />
        </div>
      </div>

      {/* Submit */}
      <button
        disabled={loading}
        onClick={handleSubmitClick}
        className="w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
      >
        {loading ? "⏳ Sending..." : "🚀 Send"}
      </button>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        title="Confirm Webhook Send"
        message={`Are you sure you want to send ${data.a} signal for ${data.s}?`}
        confirmText="Send Webhook"
        cancelText="Cancel"
        onConfirm={handleConfirmSubmit}
        onCancel={handleCancelSubmit}
        type="warning"
        data={{
          action: data.a,
          symbol: data.s,
          price: data.p,
          stopLoss: data.sl,
          comment: data.c,
          rows: rows
        }}
      />
    </div>
  );
}
